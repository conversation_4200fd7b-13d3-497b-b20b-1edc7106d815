#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的文章内容提取功能
"""

import os
import sys
from batch_readnum_spider import BatchReadnumSpider

def test_single_article_extraction():
    """测试单篇文章的内容提取"""
    print("🧪 测试改进后的文章内容提取功能")
    print("="*60)
    
    # 初始化爬虫
    spider = BatchReadnumSpider()
    
    # 检查认证信息
    if not spider.load_auth_info():
        print("❌ 认证信息加载失败，请先运行cookie抓取")
        return False
    
    print("✅ 认证信息加载成功")
    
    # 获取文章列表
    print("\n📋 获取文章列表...")
    articles = spider.get_article_list(begin_page=0, count=3)
    
    if not articles:
        print("❌ 未获取到文章列表")
        return False
    
    print(f"✅ 获取到 {len(articles)} 篇文章")
    
    # 显示文章列表供用户选择
    for i, article in enumerate(articles):
        print(f"  {i+1}. {article['title'][:80]}...")
    
    # 让用户选择要测试的文章
    try:
        choice = int(input(f"\n请选择要测试的文章 (1-{len(articles)}): ")) - 1
        if choice < 0 or choice >= len(articles):
            print("❌ 无效选择")
            return False
    except:
        print("❌ 输入无效")
        return False
    
    selected_article = articles[choice]
    print(f"\n🎯 选择的文章: {selected_article['title']}")
    print(f"🔗 URL: {selected_article['url']}")
    
    # 详细测试内容提取
    print(f"\n{'='*60}")
    print("🔍 开始详细测试内容提取...")
    
    result = spider.extract_article_content_and_stats(selected_article['url'])
    
    if result:
        print(f"\n✅ 提取结果:")
        print(f"📖 标题: {result.get('title', 'N/A')}")
        print(f"📄 内容长度: {len(result.get('content', ''))} 字符")
        print(f"📊 阅读量: {result.get('read_count', 'N/A')}")
        print(f"👍 点赞数: {result.get('like_count', 'N/A')}")
        print(f"📤 分享数: {result.get('share_count', 'N/A')}")
        print(f"⏰ 发布时间: {result.get('publish_time', 'N/A')}")
        
        content = result.get('content', '')
        if content:
            print(f"\n📝 文章内容预览:")
            print("-" * 60)
            # 显示前500字符
            preview = content[:500]
            print(preview)
            if len(content) > 500:
                print("...")
                print(f"\n[内容总长度: {len(content)} 字符]")
            print("-" * 60)
            
            # 分析内容质量
            lines = content.split('\n')
            non_empty_lines = [line.strip() for line in lines if line.strip()]
            
            print(f"\n📊 内容分析:")
            print(f"  总行数: {len(lines)}")
            print(f"  非空行数: {len(non_empty_lines)}")
            print(f"  平均行长度: {sum(len(line) for line in non_empty_lines) / len(non_empty_lines) if non_empty_lines else 0:.1f} 字符")
            
            # 检查是否包含常见的文章元素
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in content)
            has_punctuation = any(char in '。！？，；：' for char in content)
            has_paragraphs = len(non_empty_lines) > 3
            
            print(f"  包含中文: {'✅' if has_chinese else '❌'}")
            print(f"  包含标点: {'✅' if has_punctuation else '❌'}")
            print(f"  有段落结构: {'✅' if has_paragraphs else '❌'}")
            
            # 内容质量评分
            quality_score = 0
            if len(content) > 100:
                quality_score += 1
            if has_chinese:
                quality_score += 1
            if has_punctuation:
                quality_score += 1
            if has_paragraphs:
                quality_score += 1
            if len(non_empty_lines) > 10:
                quality_score += 1
            
            print(f"  内容质量评分: {quality_score}/5 {'✅' if quality_score >= 3 else '⚠️' if quality_score >= 2 else '❌'}")
            
        else:
            print("⚠️ 未提取到内容")
            
        # 检查是否有错误
        if result.get('error'):
            print(f"⚠️ 错误信息: {result['error']}")
            
        # 检查调试文件
        debug_dir = "./debug"
        if os.path.exists(debug_dir):
            debug_files = [f for f in os.listdir(debug_dir) if f.startswith('article_content_')]
            if debug_files:
                latest_file = max(debug_files)
                print(f"\n🔍 调试HTML文件: {os.path.join(debug_dir, latest_file)}")
                print("💡 可以查看此文件分析HTML结构")
                
    else:
        print("❌ 提取失败")
    
    return True

def test_batch_extraction():
    """测试批量内容提取"""
    print("🧪 测试批量内容提取功能")
    print("="*60)
    
    spider = BatchReadnumSpider()
    
    # 检查认证信息
    if not spider.load_auth_info():
        print("❌ 认证信息加载失败")
        return False
    
    # 批量抓取少量文章进行测试
    print("📋 开始批量测试（2篇文章）...")
    results = spider.batch_crawl_readnum(max_pages=1, articles_per_page=2, days_back=30)
    
    if results:
        print(f"\n📊 批量测试结果:")
        print(f"总文章数: {len(results)}")
        
        content_success = 0
        content_empty = 0
        content_short = 0
        
        for i, result in enumerate(results):
            content = result.get('content', '')
            content_len = len(content) if content else 0
            
            if content_len > 200:
                content_success += 1
                status = "✅ 成功"
            elif content_len > 50:
                content_short += 1
                status = "⚠️ 较短"
            else:
                content_empty += 1
                status = "❌ 失败"
            
            print(f"  {i+1}. {status} {result.get('title', 'N/A')[:50]}... ({content_len}字符)")
            
            if result.get('error'):
                print(f"      错误: {result['error']}")
        
        print(f"\n📈 统计:")
        print(f"  内容提取成功: {content_success}")
        print(f"  内容较短: {content_short}")
        print(f"  内容为空: {content_empty}")
        print(f"  成功率: {content_success/len(results)*100:.1f}%")
        
        # 保存测试结果
        excel_file = spider.save_to_excel()
        if excel_file:
            print(f"\n💾 测试结果已保存到: {excel_file}")
    else:
        print("❌ 批量测试失败")

def main():
    """主函数"""
    print("🚀 改进版文章内容提取测试工具")
    print("="*60)
    
    while True:
        print("\n请选择测试模式:")
        print("1. 单篇文章详细测试")
        print("2. 批量文章测试")
        print("3. 退出")
        
        try:
            choice = input("请输入选择 (1-3): ").strip()
            
            if choice == "1":
                test_single_article_extraction()
            elif choice == "2":
                test_batch_extraction()
            elif choice == "3":
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
