# 微信公众号文章内容提取功能改进

## 问题描述
原始代码只能提取文章标题和阅读量等统计信息，无法获取文章正文内容。

## 解决方案
针对微信公众号的反爬虫机制，改进了文章内容提取功能：

### 主要改进点

1. **处理隐藏内容**
   - 微信文章内容容器通常设置为 `visibility: hidden; opacity: 0;`
   - 改进后的代码能够识别并提取隐藏的内容容器

2. **多种提取方法**
   - BeautifulSoup解析（主要方法）
   - 正则表达式匹配（备用方法）
   - 全页面文本提取（最后手段）

3. **更好的HTML清理**
   - 保留段落结构
   - 清理HTML实体
   - 移除脚本和样式标签

4. **内容质量检测**
   - 检查内容长度
   - 验证中文字符
   - 分析段落结构

## 使用方法

### 1. 测试单篇文章内容提取
```bash
python test_improved_extraction.py
```
选择模式1，然后选择要测试的文章。

### 2. 批量内容提取
```bash
python batch_readnum_spider.py
```
或者使用测试工具的批量模式。

### 3. 调试内容提取
```bash
python debug_content_crawler.py
```
提供详细的调试信息和HTML文件分析。

## 文件说明

- `batch_readnum_spider.py` - 主要的爬虫类，已改进内容提取功能
- `test_improved_extraction.py` - 测试改进后的内容提取功能
- `debug_content_crawler.py` - 调试工具，提供详细的提取过程信息
- `test_content_extraction.py` - 基础测试工具

## 改进的方法

### `extract_article_content()`
- 使用BeautifulSoup进行HTML解析
- 支持多种内容容器选择器
- 处理隐藏内容容器
- 提供正则表达式备用方案

### `extract_article_content_and_stats()`
- 增加了备用内容提取方法
- 支持从隐藏容器提取内容
- 提供全页面文本提取作为最后手段

### `clean_html_content()`
- 改进的HTML清理算法
- 保留段落结构
- 处理HTML实体
- 更好的空白字符处理

## 调试功能

程序会自动保存HTML文件到 `./debug/` 目录，文件名格式为 `article_content_YYYYMMDD_HHMMSS.html`。

可以通过查看这些文件来：
- 分析微信文章的HTML结构
- 检查内容是否被隐藏
- 调试提取算法

## 内容质量评估

测试工具会对提取的内容进行质量评估：
- 内容长度检查
- 中文字符检测
- 标点符号检测
- 段落结构分析
- 质量评分（1-5分）

## 常见问题

### 1. 内容为空或过短
- 检查cookie是否有效
- 确认文章URL是否完整
- 查看调试HTML文件分析结构

### 2. 遇到验证码
- 降低抓取频率
- 增加延迟时间
- 手动完成验证后重试

### 3. 内容提取不完整
- 检查HTML结构是否发生变化
- 尝试不同的提取方法
- 查看调试信息

## 技术细节

### 内容容器识别
程序会尝试以下选择器：
- `div.rich_media_content`
- `div#js_content`
- `div.js_content`
- `div[data-role="content"]`
- `section[data-role="content"]`
- `div.rich_media_content.js_underline_content`

### 隐藏内容检测
检查元素的style属性：
- `visibility: hidden`
- `opacity: 0`

### 备用提取方法
1. BeautifulSoup解析
2. 正则表达式匹配
3. HTML清理方法
4. 全页面文本提取

## 注意事项

1. **频率控制**：保持适当的请求间隔，避免被封
2. **Cookie有效性**：定期更新cookie信息
3. **内容验证**：检查提取的内容质量
4. **调试信息**：利用调试文件分析问题

## 更新日志

- 2025-08-01: 改进内容提取功能，支持隐藏内容提取
- 增加多种备用提取方法
- 改进HTML清理算法
- 添加内容质量评估
- 提供详细的调试工具
