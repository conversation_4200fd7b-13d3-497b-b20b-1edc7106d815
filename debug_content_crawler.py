#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版文章内容抓取器
专门用于调试和测试文章内容提取功能
"""

import os
import sys
import time
from batch_readnum_spider import BatchReadnumSpider

def debug_single_article():
    """调试单篇文章的内容提取"""
    print("🔍 单篇文章内容提取调试")
    print("="*50)
    
    spider = BatchReadnumSpider()
    
    # 检查认证信息
    if not spider.load_auth_info():
        print("❌ 认证信息加载失败")
        return
    
    # 获取文章列表
    articles = spider.get_article_list(begin_page=0, count=5)
    if not articles:
        print("❌ 未获取到文章列表")
        return
    
    print(f"📋 获取到 {len(articles)} 篇文章:")
    for i, article in enumerate(articles):
        print(f"  {i+1}. {article['title'][:60]}...")
    
    # 让用户选择要调试的文章
    try:
        choice = int(input(f"\n请选择要调试的文章 (1-{len(articles)}): ")) - 1
        if choice < 0 or choice >= len(articles):
            print("❌ 无效选择")
            return
    except:
        print("❌ 输入无效")
        return
    
    selected_article = articles[choice]
    print(f"\n🎯 选择的文章: {selected_article['title']}")
    print(f"🔗 URL: {selected_article['url']}")
    
    # 详细调试内容提取过程
    print(f"\n{'='*60}")
    print("🔍 开始详细调试...")
    
    result = spider.extract_article_content_and_stats(selected_article['url'])
    
    if result:
        print(f"\n✅ 提取结果:")
        print(f"📖 标题: {result.get('title', 'N/A')}")
        print(f"📄 内容长度: {len(result.get('content', ''))} 字符")
        print(f"📊 阅读量: {result.get('read_count', 'N/A')}")
        print(f"👍 点赞数: {result.get('like_count', 'N/A')}")
        print(f"📤 分享数: {result.get('share_count', 'N/A')}")
        print(f"⏰ 发布时间: {result.get('publish_time', 'N/A')}")
        
        content = result.get('content', '')
        if content:
            print(f"\n📝 文章内容:")
            print("-" * 60)
            print(content[:1000] + ("..." if len(content) > 1000 else ""))
            print("-" * 60)
            print(f"总长度: {len(content)} 字符")
        else:
            print("⚠️ 未提取到内容")
            
        # 检查调试文件
        debug_dir = "./debug"
        if os.path.exists(debug_dir):
            debug_files = [f for f in os.listdir(debug_dir) if f.startswith('article_content_')]
            if debug_files:
                latest_file = max(debug_files)
                print(f"\n🔍 调试HTML文件: {os.path.join(debug_dir, latest_file)}")
                print("💡 可以查看此文件分析HTML结构")
    else:
        print("❌ 提取失败")

def batch_debug_content():
    """批量调试内容提取"""
    print("🔍 批量内容提取调试")
    print("="*50)
    
    spider = BatchReadnumSpider()
    
    # 检查认证信息
    if not spider.load_auth_info():
        print("❌ 认证信息加载失败")
        return
    
    # 批量抓取少量文章进行调试
    print("📋 开始批量调试（3篇文章）...")
    results = spider.batch_crawl_readnum(max_pages=1, articles_per_page=3, days_back=30)
    
    if results:
        print(f"\n📊 调试结果统计:")
        print(f"总文章数: {len(results)}")
        
        content_success = 0
        content_empty = 0
        content_error = 0
        
        for result in results:
            content = result.get('content', '')
            if content and len(content.strip()) > 50:
                content_success += 1
            elif not content or len(content.strip()) <= 50:
                content_empty += 1
            if result.get('error'):
                content_error += 1
        
        print(f"内容提取成功: {content_success}")
        print(f"内容为空/过短: {content_empty}")
        print(f"提取错误: {content_error}")
        
        # 显示详细结果
        print(f"\n📋 详细结果:")
        for i, result in enumerate(results):
            content = result.get('content', '')
            content_len = len(content) if content else 0
            status = "✅" if content_len > 50 else "⚠️" if content_len > 0 else "❌"
            
            print(f"  {i+1}. {status} {result.get('title', 'N/A')[:50]}... ({content_len}字符)")
            if result.get('error'):
                print(f"      错误: {result['error']}")
        
        # 保存调试结果
        excel_file = spider.save_to_excel()
        if excel_file:
            print(f"\n💾 调试结果已保存到: {excel_file}")
    else:
        print("❌ 批量调试失败")

def main():
    """主函数"""
    print("🚀 文章内容提取调试工具")
    print("="*60)
    
    while True:
        print("\n请选择调试模式:")
        print("1. 单篇文章详细调试")
        print("2. 批量文章调试")
        print("3. 退出")
        
        try:
            choice = input("请输入选择 (1-3): ").strip()
            
            if choice == "1":
                debug_single_article()
            elif choice == "2":
                batch_debug_content()
            elif choice == "3":
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
