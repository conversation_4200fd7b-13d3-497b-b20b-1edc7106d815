#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文章内容提取功能
"""

import os
import sys
from batch_readnum_spider import BatchReadnumSpider

def test_content_extraction():
    """测试文章内容提取功能"""
    print("🧪 测试文章内容提取功能")
    print("="*50)
    
    # 初始化爬虫
    spider = BatchReadnumSpider()
    
    # 检查认证信息
    if not spider.load_auth_info():
        print("❌ 认证信息加载失败，请先运行cookie抓取")
        return False
    
    print("✅ 认证信息加载成功")
    
    # 获取少量文章进行测试
    print("\n📋 获取文章列表进行测试...")
    articles = spider.get_article_list(begin_page=0, count=3)
    
    if not articles:
        print("❌ 未获取到文章列表")
        return False
    
    print(f"✅ 获取到 {len(articles)} 篇文章")
    
    # 测试每篇文章的内容提取
    for i, article in enumerate(articles):
        print(f"\n{'='*60}")
        print(f"🧪 测试文章 {i+1}: {article['title'][:50]}...")
        print(f"🔗 URL: {article['url'][:80]}...")
        
        # 提取文章内容和统计数据
        result = spider.extract_article_content_and_stats(article['url'])
        
        if result:
            print(f"✅ 提取成功!")
            print(f"📖 标题: {result.get('title', 'N/A')}")
            print(f"📄 内容长度: {len(result.get('content', ''))} 字符")
            print(f"📊 阅读量: {result.get('read_count', 'N/A')}")
            print(f"👍 点赞数: {result.get('like_count', 'N/A')}")
            print(f"📤 分享数: {result.get('share_count', 'N/A')}")
            print(f"⏰ 发布时间: {result.get('publish_time', 'N/A')}")
            
            # 显示内容预览
            content = result.get('content', '')
            if content and len(content) > 100:
                print(f"📝 内容预览: {content[:200]}...")
            elif content:
                print(f"📝 完整内容: {content}")
            else:
                print("⚠️ 未提取到内容")
                
            # 检查是否有错误
            if result.get('error'):
                print(f"⚠️ 错误信息: {result['error']}")
                
        else:
            print("❌ 提取失败")
        
        # 测试间隔
        if i < len(articles) - 1:
            print("⏳ 等待5秒后测试下一篇...")
            import time
            time.sleep(5)
    
    print(f"\n{'='*60}")
    print("🎉 测试完成!")
    return True

def test_html_parsing():
    """测试HTML解析功能"""
    print("\n🧪 测试HTML解析功能")
    print("="*50)
    
    # 创建测试HTML内容
    test_html = '''
    <html>
    <head>
        <meta property="og:title" content="测试文章标题">
    </head>
    <body>
        <div class="rich_media_content">
            <p>这是第一段内容。</p>
            <p>这是第二段内容，包含<strong>加粗文字</strong>和<em>斜体文字</em>。</p>
            <div>这是一个div中的内容。</div>
            <br>
            <p>这是换行后的内容。</p>
        </div>
    </body>
    </html>
    '''
    
    spider = BatchReadnumSpider()
    
    # 测试内容提取
    content = spider.extract_article_content(test_html)
    print(f"✅ 提取的内容:")
    print(content)
    print(f"📏 内容长度: {len(content)} 字符")
    
    return True

if __name__ == "__main__":
    print("🚀 文章内容提取测试工具")
    print("="*60)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 测试HTML解析功能（离线测试）")
    print("2. 测试实际文章内容提取（需要网络和认证）")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            test_html_parsing()
        elif choice == "2":
            test_content_extraction()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 测试已取消")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
